<?php

declare(strict_types=1);

namespace App\Library\Tabulator;

/**
 * Class ArrayResult.
 *
 * This class encapsulates the data returned by a query, along with optional pagination information
 * such as the total number of pages and the total number of records.
 */
readonly class ArrayResult implements ResultInterface
{

    public function __construct(
        private array $data,
        private ?int $totalPages = null,
        private ?int $totalRecords = null,
    ) {
    }

    /**
     * Retrieves data.
     *
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * Retrieves total pages.
     *
     * @return int|null
     */
    public function getTotalPages(): ?int
    {
        return $this->totalPages;
    }

    /**
     * Retrieves total records.
     *
     * @return int|null
     */
    public function getTotalRecords(): ?int
    {
        return $this->totalRecords;
    }

}
