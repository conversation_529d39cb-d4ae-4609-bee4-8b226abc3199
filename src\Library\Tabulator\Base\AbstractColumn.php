<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Base;

use InvalidArgumentException;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Abstract class AbstractColumn.
 *
 * This abstract class provides the base functionality for a table column definition,
 * including methods for configuring options, setting default values, and managing
 * additional options.
 */
abstract class AbstractColumn
{

    private array $options = [];

    /**
     * Prepare the content.
     *
     * @param $value
     *
     * @return mixed
     */
    abstract public function prepareContent($value): mixed;

    /**
     * Retrieves the option array.
     *
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Retrieves an option by the given name.
     *
     * @param  string  $name
     *
     * @return mixed
     */
    public function getOption(string $name): mixed
    {
        if (!array_key_exists($name, $this->options)) {
            throw new InvalidArgumentException("Option '$name' does not exist");
        }

        return $this->options[$name];
    }

    /**
     * Set options.
     *
     * @param  array  $options
     *
     * @return $this
     */
    public function setOptions(array $options): AbstractColumn
    {
        $resolver = new OptionsResolver();
        $this->configureOptions($resolver);

        $this->options = $resolver->resolve($options);

        return $this;
    }

    /**
     * Retrieves the config.
     *
     * @return array
     */
    public function getConfig(): array
    {
        return array_merge_recursive([
            'title'   => $this->getOption('title') ?? '',
            'field'   => $this->getOption('field'),
            'visible' => $this->getOption('visible'),
        ], $this->getDefaultConfig(), $this->getOption('extra'));
    }

    /**
     * Retrieves the default config.
     *
     * @return array
     */
    protected function getDefaultConfig(): array
    {
        return [];
    }

    /**
     * Set the config options.
     *
     * @param  OptionsResolver  $resolver
     *
     * @return void
     */
    protected function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setRequired(['title', 'field'])
            ->setDefaults(['visible' => true, 'filterable' => true, 'extra' => []])
            ->setAllowedTypes('title', ['string', 'null', 'bool'])
            ->setAllowedTypes('field', 'string')
            ->setAllowedTypes('visible', 'bool')
            ->setAllowedTypes('filterable', 'bool')
            ->setAllowedTypes('extra', 'array');
    }

}
