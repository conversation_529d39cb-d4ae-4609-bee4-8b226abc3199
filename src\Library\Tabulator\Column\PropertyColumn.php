<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\PropertyAccess\PropertyAccess;

/**
 * Class PropertyColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined property-related function.
 */
class PropertyColumn extends AbstractColumn
{

    protected function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver->setRequired('property')
            ->setDefaults(['nullValue' => null])
            ->setAllowedTypes('property', 'string')
            ->setAllowedTypes('nullValue', ['string', 'null']);
    }

    public function prepareContent($value): mixed
    {
        if (null === $value) {
            return $this->getOption('nullValue');
        }

        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        return $propertyAccessor->getValue($value, $this->getOption('property')) ?? $this->getOption('nullValue');
    }

}
