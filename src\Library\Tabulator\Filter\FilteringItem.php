<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Filter;

use App\Library\Tabulator\Base\AbstractColumn;
use App\Library\Tabulator\Enum\FilteringType;

/**
 * Class FilteringItem.
 *
 * Represents an individual filter condition in the filtering process.
 * Holds the necessary data for a filter, including the field, comparison operator,
 * and the value to compare against.
 */
class FilteringItem
{

    private ?AbstractColumn $column = null;

    private FilteringType $type;

    private mixed $value;

    /**
     * Retrieves the filter column.
     *
     * @return AbstractColumn|null
     */
    public function getColumn(): ?AbstractColumn
    {
        return $this->column;
    }

    /**
     * Set the filter column.
     *
     * @param  AbstractColumn|null  $column
     *
     * @return static
     */
    public function setColumn(?AbstractColumn $column): static
    {
        $this->column = $column;

        return $this;
    }

    /**
     * Retrieves the filter type.
     *
     * @return FilteringType
     */
    public function getType(): FilteringType
    {
        return $this->type;
    }

    /**
     * Set the filter type.
     *
     * @param  FilteringType  $type
     *
     * @return static
     */
    public function setType(FilteringType $type): static
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Retrieves the filter value.
     *
     * @return mixed
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    /**
     * Set the filter value.
     *
     * @param  mixed  $value
     *
     * @return static
     */
    public function setValue(mixed $value): static
    {
        $this->value = $value;

        return $this;
    }

}
