<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Base;

use App\Library\Tabulator\Adapter\QueryAdapter;
use App\Library\Tabulator\ResultInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use InvalidArgumentException;

/**
 * Abstract class AbstractAdapter.
 *
 * This abstract class defines the basic structure and functionality for any data adapter
 * that interacts with a data source (such as a database, API, or in-memory data).
 */
abstract class AbstractAdapter
{

    private array $options = [];

    /**
     * Retrieves option array.
     *
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Retrieves option by the given name.
     *
     * @param  string  $name
     *
     * @return mixed
     */
    public function getOption(string $name): mixed
    {
        if (!array_key_exists($name, $this->options)) {
            throw new InvalidArgumentException("Option '$name' does not exist");
        }

        return $this->options[$name];
    }

    /**
     * Set option data.
     *
     * @param  array  $options
     *
     * @return $this
     */
    public function setOptions(array $options): static
    {
        $resolver = new OptionsResolver();
        $this->configureOptions($resolver);
        $this->options = $resolver->resolve($options);

        return $this;
    }

    abstract public function getData(QueryAdapter $adapterQuery): ResultInterface;

    abstract protected function configureOptions(OptionsResolver $resolver): void;

}
