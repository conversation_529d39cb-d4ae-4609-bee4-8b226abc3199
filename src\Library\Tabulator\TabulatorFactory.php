<?php

declare(strict_types=1);

namespace App\Library\Tabulator;

use App\Library\Tabulator\Parser\ParserInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class TabulatorFactory.
 *
 * Factory class responsible for creating instances of the Tabulator.
 * It leverages the `RequestStack`, `InstanceStorage`, and `ParserInterface` dependencies
 * to configure and return fully initialized `Tabulator` instances.
 */
readonly class TabulatorFactory
{

    public function __construct(
        private RequestStack $requestStack,
        private InstanceStorage $instanceStorage,
        private ParserInterface $parser,
    ) {
    }

    /**
     * Create the tabulator instance.
     *
     * @param  string  $selector
     * @param  array   $options
     *
     * @return Tabulator
     */
    public function create(string $selector, array $options = []): Tabulator
    {
        return new Tabulator($selector, $this->requestStack->getCurrentRequest(), $this->instanceStorage, $this->parser)
            ->setOptions($options);
    }

}
