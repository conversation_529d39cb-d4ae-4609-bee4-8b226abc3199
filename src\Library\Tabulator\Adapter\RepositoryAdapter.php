<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Adapter;

use App\Library\Tabulator\ArrayResult;
use App\Library\Tabulator\Base\AbstractAdapter;
use App\Library\Tabulator\Enum\FilteringComparison;
use App\Library\Tabulator\Enum\FilteringType;
use App\Library\Tabulator\ResultInterface;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use InvalidArgumentException;
use RuntimeException;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class RepositoryAdapter.
 *
 * This adapter allows you to retrieve data from a Doctrine repository with support for
 *  filtering, sorting, and pagination.
 */
class RepositoryAdapter extends AbstractAdapter
{

    private ?ManagerRegistry $managerRegistry = null;

    public function __construct(?ManagerRegistry $managerRegistry = null)
    {
        if (null === $managerRegistry) {
            throw new InvalidArgumentException('Install doctrine/doctrine-bundle to use the RepositoryAdapter');
        }

        $this->managerRegistry = $managerRegistry;
    }

    public function getData(QueryAdapter $adapterQuery): ResultInterface
    {
        $queryBuilder = $this->getQueryBuilder($adapterQuery);

        if (!$queryBuilder instanceof QueryBuilder && !$queryBuilder instanceof ResultInterface) {
            throw new InvalidArgumentException(
                'The query_builder option must return an instance of QueryBuilder or ResultInterface'
            );
        }

        if ($queryBuilder instanceof ResultInterface) {
            return $queryBuilder;
        }

        $rootAlias = $queryBuilder->getRootAliases()[0];

        if ($adapterQuery->getFilteringBag()->hasFiltering()) {
            $this->queryWithFiltering($adapterQuery, $queryBuilder, $rootAlias);
        }

        if ($adapterQuery->getSortingBag()->hasSorting()) {
            foreach ($adapterQuery->getSortingBag()->getSortingItems() as $sortingItem) {
                $queryFieldName = sprintf('%s.%s', $rootAlias, $sortingItem->getColumn()->getOption('field'));

                $queryBuilder->addOrderBy($queryFieldName, $sortingItem->getDirection()->value);
            }
        }

        if ($adapterQuery->isPagination()) {
            $queryBuilder
                ->setFirstResult(
                    $adapterQuery->getPaginationSize() * $adapterQuery->getPaginationPage()
                    - $adapterQuery->getPaginationSize()
                )
                ->setMaxResults($adapterQuery->getPaginationSize());

            $queryPaginator = new Paginator($queryBuilder);
            $queryResult = $queryBuilder->getQuery()->getResult();

            return new ArrayResult(
                $queryResult,
                (int)ceil($queryPaginator->count() / $adapterQuery->getPaginationSize()),
                $queryPaginator->count()
            );
        }

        return new ArrayResult($queryBuilder->getQuery()->getResult());
    }

    protected function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setRequired(['entity', 'query_builder'])
            ->setAllowedTypes('entity', 'string')
            ->setAllowedTypes('query_builder', 'callable');
    }

    /**
     * Parse FilteringType to QueryBuilder expression.
     *
     * @param  FilteringType  $type
     *
     * @return string|null
     */
    private function getQueryExpression(FilteringType $type): ?string
    {
        return match ($type) {
            FilteringType::EQUAL => 'eq',
            FilteringType::NOT_EQUAL => 'neq',
            FilteringType::LIKE => 'like',
            FilteringType::LESS => 'lt',
            FilteringType::LESS_OR_EQUAL => 'lte',
            FilteringType::GREATER => 'gt',
            FilteringType::GREATER_OR_EQUAL => 'gte',

            default => null
        };
    }

    /**
     * Prepare value for Query Parameter.
     *
     * @param  FilteringType  $type
     * @param  mixed          $value
     *
     * @return mixed
     */
    private function prepareQueryParameter(FilteringType $type, mixed $value): mixed
    {
        return match ($type) {
            FilteringType::LIKE => sprintf('%%%s%%', $value),

            default => $value
        };
    }

    /**
     * Query with filtering.
     *
     * @param  QueryAdapter                  $adapterQuery
     * @param  ResultInterface|QueryBuilder  $queryBuilder
     * @param  mixed                         $rootAlias
     *
     * @return void
     */
    private function queryWithFiltering(
        QueryAdapter $adapterQuery,
        ResultInterface|QueryBuilder $queryBuilder,
        mixed $rootAlias
    ): void {
        $andExpression = $queryBuilder->expr()->andX();
        $orExpression = $queryBuilder->expr()->orX();

        foreach ($adapterQuery->getFilteringBag()->getFilters() as $comparison => $filterItems) {
            foreach ($filterItems as $filterItem) {
                $filterValue = $filterItem->getValue();
                $filterType = $filterItem->getType();

                if (!empty($filterValue) && $filterItem->getColumn() !== null) {
                    $queryExpression = $this->getQueryExpression($filterType);
                    $queryFieldName = sprintf('%s.%s', $rootAlias, $filterItem->getColumn()->getOption('field'));
                    $queryFilterParameter = uniqid('_param_', true);

                    if (null === $queryExpression) {
                        throw new InvalidArgumentException(
                            'Provided filtering type is not supported by the RepositoryAdapter'
                        );
                    }

                    ${$comparison === FilteringComparison::OR->value ? 'orExpression' : 'andExpression'}->add(
                        $queryBuilder->expr()->{$queryExpression}(
                            $queryFieldName,
                            sprintf(':%s', $queryFilterParameter)
                        )
                    );

                    $queryBuilder->setParameter(
                        $queryFilterParameter,
                        $this->prepareQueryParameter($filterType, $filterValue)
                    );
                }
            }
        }

        $queryBuilder->andWhere($andExpression)->andWhere($orExpression);
    }

    /**
     * Retrieves the QueryBuilder.
     *
     * @param  QueryAdapter  $adapterQuery
     *
     * @return ResultInterface|QueryBuilder
     */
    private function getQueryBuilder(QueryAdapter $adapterQuery): ResultInterface|QueryBuilder
    {
        $repository = $this->managerRegistry?->getManagerForClass($this->getOption('entity'))?->getRepository(
            $this->getOption('entity')
        );

        if (null === $repository) {
            throw new RuntimeException("Repository for entity '{$this->getOption("entity")}' not found");
        }

        return call_user_func($this->getOption('query_builder'), $repository, $adapterQuery);
    }

}
