<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Twig;

use App\Library\Tabulator\Tabulator;
use JsonException;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Class TabulatorExtension.
 *
 * This class extends the Twig functionality by adding a custom function for generating the JSON configuration
 * of the `Tabulator` instance.
 */
class TabulatorExtension extends AbstractExtension
{

    /**
     * Configuration.
     *
     * @throws JsonException
     */
    public function config(Tabulator $tabulator): string
    {
        return json_encode($tabulator->getConfig(), JSON_THROW_ON_ERROR);
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('tabulator_config', [$this, 'config']),
        ];
    }

}
