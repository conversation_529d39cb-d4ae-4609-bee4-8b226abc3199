/* Remove border from first column */
.tabulator .tabulator-row .tabulator-cell:first-child {
    border-bottom: none !important;
    border-right: none;
}


.tabulator-row .tabulator-cell.tabulator-row-header {
    background: none;
}

/* Remove border from last column */
.tabulator .tabulator-header {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}


.tabulator-row.tabulator-group {
    border: none !important;
}

.tabulator .tabulator-group {
    border-bottom: none !important;
}

/* Custom Tabulator Styling */
.tabulator {
    background: transparent !important;
    border: none !important;
}

.dark .tabulator .tabulator-responsive-collapse td {
    color: white !important;
}
.tabulator .tabulator-header {
    background: rgb(226 232 240) !important;
    border: none !important;
}

.dark .tabulator .tabulator-header {
    background: rgb(30 41 59) !important;
}

.tabulator .tabulator-header .tabulator-col {
    background: rgb(226 232 240) !important;
    color: rgb(30 41 59) !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 0.875rem !important;
    padding: 12px 16px !important;
    border-right: none !important;
}

.dark .tabulator .tabulator-header .tabulator-col {
    background: rgb(30 41 59) !important;
    color: rgb(148 163 184) !important;
}

.tabulator .tabulator-header .tabulator-col:first-child {
    border-top-left-radius: 0.5rem !important;
}

.tabulator .tabulator-header .tabulator-col:last-child {
    border-top-right-radius: 0.5rem !important;
}

.tabulator .tabulator-row {
    background: white !important;
    border-bottom: 1px solid rgb(226 232 240) !important;
}

.dark .tabulator .tabulator-row {
    background: #25334d !important;
    border-bottom: 1px solid rgb(71 85 105) !important;
}

.tabulator .tabulator-row:hover {
    background: rgb(248 250 252) !important;
}

.dark .tabulator .tabulator-row:hover {
    background: #303e59 !important;
}

.tabulator .tabulator-row.tabulator-selected {
    background: rgb(239 246 255) !important;
}

.dark .tabulator .tabulator-row.tabulator-selected {
    background: #1e3a8a !important;
}

.tabulator .tabulator-cell {
    padding: 12px 16px;
    border-right: none;
    color: rgb(51 65 85) !important;
}

.dark .tabulator .tabulator-cell {
    color: rgb(148 163 184) !important;
}

.tabulator .tabulator-footer {
    background: white !important;
    border: none !important;
    border-bottom-left-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
}

.dark .tabulator .tabulator-footer {
    background: rgb(15 23 42) !important;
}

/* Hide default pagination */
.tabulator .tabulator-paginator {
    display: none !important;
}

/* Custom styles */
.card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.dark .card {
    background: rgb(15 23 42);
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

/* Role Badge Styles */
.badge-admin {
    background: rgb(239 68 68 / 0.1);
    color: rgb(239 68 68);
}

.dark .badge-admin {
    background: rgb(239 68 68 / 0.15);
    color: rgb(248 113 113);
}

.badge-reseller {
    background: rgb(59 130 246 / 0.1);
    color: rgb(59 130 246);
}

.dark .badge-reseller {
    background: rgb(59 130 246 / 0.15);
    color: rgb(96 165 250);
}

.badge-client {
    background: rgb(16 185 129 / 0.1);
    color: rgb(16 185 129);
}

.dark .badge-client {
    background: rgb(16 185 129 / 0.15);
    color: rgb(52 211 153);
}

.badge-partner {
    background: rgb(245 158 11 / 0.1);
    color: rgb(245 158 11);
}

.dark .badge-partner {
    background: rgb(245 158 11 / 0.15);
    color: rgb(251 191 36);
}

.badge-user {
    background: rgb(156 163 175 / 0.1);
    color: rgb(156 163 175);
}

.dark .badge-user {
    background: rgb(156 163 175 / 0.15);
    color: rgb(209 213 219);
}

.form-switch {
    appearance: none;
    position: relative;
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 9999px;
    background: rgb(203 213 225);
    transition: all 0.2s;
    cursor: pointer;
}

.form-switch:before {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: white;
    transition: all 0.2s;
}

.form-switch:checked {
    background: rgb(59 130 246);
}

.form-switch:checked:before {
    transform: translateX(1.25rem);
}

.dark .form-switch {
    background: rgb(30 41 59);
}

.dark .form-switch:before {
    background: rgb(100 116 139);
}

.dark .form-switch:checked {
    background: rgb(6 182 212);
}

.dark .form-switch:checked:before {
    background: white;
}

.popper-root {
    display: none;
    position: absolute;
    z-index: 50;
}

.popper-root.show {
    display: block;
}

.popper-box {
    min-width: 8rem;
}

/* Mobile dropdown menu styles */
.mobile-dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 5 !important;
    position: absolute !important;
}

.mobile-dropdown-item {
    transition: all 0.15s ease;
}

.mobile-dropdown-item:active {
    transform: scale(0.98);
}

/* Ensure bulk actions container has proper stacking */
.bulk-actions {
    position: relative;
    z-index: 4;
    overflow: visible !important;
}

/* Override any table z-index issues */
.tabulator {
    position: relative;
    z-index: 1;
}

/* Ensure card container allows overflow */
.card {
    overflow: visible !important;
}

/* Ensure main container allows overflow for dropdowns */
.container {
    overflow: visible !important;
}

/* Global override for any hidden overflow that might interfere */
body, html {
    overflow-x: hidden;
    overflow-y: auto;
}

/* Bulk Actions Styles */
.bulk-actions {
    background: #4f46e5;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.3s ease;
}

.bulk-actions.show {
    transform: translateY(0);
    opacity: 1;
}

.dark .bulk-actions {
    background: #4f46e5;
}

.bulk-actions-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
}

.bulk-actions-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.bulk-actions-button.danger {
    background: rgba(239, 68, 68, 0.8);
    border-color: rgba(239, 68, 68, 0.8);
}

.bulk-actions-button.danger:hover {
    background: rgba(220, 38, 38, 0.9);
    border-color: rgba(220, 38, 38, 0.9);
}

/* Pagination Styles */
.pagination {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.pagination li {
    margin: 0;
    padding: 0;
}

.pagination li:not(:first-child):not(:last-child) {
    margin-left: -1px; /* Overlap borders for seamless look */
}

.pagination li:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.pagination li:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

.pagination button {
    border: none;
    outline: none;
    text-decoration: none;
}

.pagination button:disabled {
    pointer-events: none;
}

/* Ensure proper z-index for hover effects */
.pagination li:hover {
    z-index: 1;
    position: relative;
}

/* Role Selection Modal Styles */
.role-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.role-modal.show {
    opacity: 1;
    visibility: visible;
}

.role-modal-content {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    max-width: 400px;
    width: 90%;
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.role-modal.show .role-modal-content {
    transform: scale(1);
}

.dark .role-modal-content {
    background: rgb(15 23 42);
    color: white;
}

.role-option {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 0.5rem;
}

.role-option:hover {
    background: rgb(248 250 252);
    border-color: rgb(203 213 225);
}

.dark .role-option:hover {
    background: rgb(30 41 59);
    border-color: rgb(71 85 105);
}

.role-option.selected {
    border-color: rgb(59 130 246);
    background: rgb(239 246 255);
}

.dark .role-option.selected {
    border-color: rgb(6 182 212);
    background: rgb(12 74 110);
}

/* Tabulator Group Headers Styling */
.tabulator .tabulator-group {
    background: rgb(248 250 252) !important; /* slate-50 */
    border-bottom: 2px solid rgb(226 232 240) !important; /* slate-200 */
    border-top: 1px solid rgb(226 232 240) !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important; /* text-sm */
    color: rgb(51 65 85) !important; /* slate-700 */
    padding: 0.75rem 1rem !important; /* py-3 px-4 */
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important; /* tracking-wider */
}

.dark .tabulator .tabulator-group {
    background: rgb(30 41 59) !important; /* slate-800 */
    border-bottom: 2px solid rgb(71 85 105) !important; /* slate-600 */
    border-top: 1px solid rgb(51 65 85) !important; /* slate-700 */
    color: rgb(203 213 225) !important; /* slate-300 */
}

/* Group toggle button (+ / -) */
.tabulator .tabulator-group .tabulator-group-toggle {
    color: rgb(59 130 246) !important; /* blue-500 */
    margin-right: 0.75rem !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
    transition: all 0.2s ease !important;
}

.dark .tabulator .tabulator-group .tabulator-group-toggle {
    color: rgb(96 165 250) !important; /* blue-400 */
}

.tabulator .tabulator-group .tabulator-group-toggle:hover {
    color: rgb(37 99 235) !important; /* blue-600 */
    transform: scale(1.1) !important;
}

.dark .tabulator .tabulator-group .tabulator-group-toggle:hover {
    color: rgb(59 130 246) !important; /* blue-500 */
}

/* Group label styling */
.tabulator .tabulator-group .tabulator-group-label {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* Hover effect for group headers */
.tabulator .tabulator-group:hover {
    background: rgb(241 245 249) !important; /* slate-100 */
    transition: background-color 0.2s ease !important;
    cursor: pointer !important;
}

.dark .tabulator .tabulator-group:hover {
    background: rgb(51 65 85) !important; /* slate-700 */
}

/* Filter Section Styling */
.filter-section {
    background: white;
    border: 1px solid rgb(226 232 240);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.dark .filter-section {
    background: rgb(15 23 42);
    border-color: rgb(71 85 105);
}

.filter-header {
    background: rgb(248 250 252);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgb(226 232 240);
    font-weight: 600;
    font-size: 0.875rem;
    color: rgb(51 65 85);
}

.dark .filter-header {
    background: rgb(30 41 59);
    border-bottom-color: rgb(71 85 105);
    color: rgb(203 213 225);
}

.filter-content {
    padding: 1rem;
}
