<?php

declare(strict_types=1);

namespace App\Library\Tabulator;

/**
 * Interface ResultInterface.
 *
 * Defines the contract for result objects returned by data adapters.
 * The result object must provide methods to access total page count, total record count,
 * and the actual data, which is usually an array of entities or values.
 */
interface ResultInterface
{

    /**
     * Retrieves total pages.
     *
     * @return int|null
     */
    public function getTotalPages(): ?int;

    /**
     * Retrieves total records.
     *
     * @return int|null
     */
    public function getTotalRecords(): ?int;

    /**
     * Retrieves data.
     *
     * @return array
     */
    public function getData(): array;

}
