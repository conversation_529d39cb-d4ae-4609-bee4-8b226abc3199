<?php

declare(strict_types=1);

namespace App\Library\Tabulator;

use App\Library\Tabulator\Base\AbstractAdapter;
use App\Library\Tabulator\Base\AbstractColumn;
use InvalidArgumentException;

/**
 * Class InstanceStorage.
 *
 * This class provides a way to retrieve pre-existing instances or create new ones dynamically based on the type
 * provided.
 */
readonly class InstanceStorage
{

    public function __construct(private array $instances = [])
    {
    }

    /**
     * Get adapter instance.
     *
     * @param  string  $type
     *
     * @return AbstractAdapter
     */
    public function getAdapter(string $type): AbstractAdapter
    {
        return $this->getInstance($type, AbstractAdapter::class);
    }

    /**
     * Get column instance by the given type.
     *
     * @param  string  $type
     *
     * @return AbstractColumn
     */
    public function getColumn(string $type): AbstractColumn
    {
        return $this->getInstance($type, AbstractColumn::class);
    }

    /**
     * Get instance by the given type and base-type.
     *
     * @param  string  $type
     * @param  string  $baseType
     *
     * @return mixed
     */
    private function getInstance(string $type, string $baseType): mixed
    {
        if (isset($this->instances[$baseType]) && $this->instances[$baseType]->has($type)) {
            $instance = clone $this->instances[$baseType]->get($type);
        } elseif (class_exists($type)) {
            $instance = new $type();
        } else {
            throw new InvalidArgumentException(sprintf('Could not resolve type "%s" to a service or class', $type));
        }

        if (!$instance instanceof $baseType) {
            throw new InvalidArgumentException(sprintf('Class "%s" must implement/extend %s', $type, $baseType));
        }

        return $instance;
    }

}
