<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Parser;

use App\Library\Tabulator\Column\TwigColumn;
use Symfony\Component\PropertyAccess\PropertyAccess;

/**
 * Class PropertyParser.
 *
 * This class is responsible for parsing raw data (adapter data) and mapping it to a structured
 * format based on the provided column definitions.
 */
class PropertyParser implements ParserInterface
{

    /**
     * Parse adapter data to columns.
     *
     * @param  array  $adapterData
     * @param  array  $columns
     *
     * @return array
     */
    public function parse(array $adapterData, array $columns): array
    {
        $propertyAccess = PropertyAccess::createPropertyAccessor();

        $parseResult = [];

        foreach ($adapterData as $dataItem) {
            $columnResult = [];

            foreach ($columns as $column) {
                $columnName = $column->getOption('field');
                $propertyPath = is_array($dataItem) ? '['.$columnName.']' : $columnName;

                $columnResult[$columnName] = $column->prepareContent(
                    $column instanceof TwigColumn && $column->getOption('passRow') ? $dataItem
                        : $propertyAccess->getValue($dataItem, $propertyPath)
                );
            }

            $parseResult[] = $columnResult;
        }

        return $parseResult;
    }

}
