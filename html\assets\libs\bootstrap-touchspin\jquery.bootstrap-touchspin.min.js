/*
 *  Bootstrap Touchspin - v4.7.3
 *  A mobile and touch friendly input spinner component for Bootstrap 3 & 4.
 *  https://www.virtuosoft.eu/code/bootstrap-touchspin/
 *
 *  Made by <PERSON><PERSON><PERSON>
 *  Under MIT License
 */
"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(o){"function"==typeof define&&define.amd?define(["jquery"],o):"object"===("undefined"==typeof module?"undefined":_typeof(module))&&module.exports?module.exports=function(t,n){return void 0===n&&(n="undefined"!=typeof window?require("jquery"):require("jquery")(t)),o(n),n}:o(jQuery)}(function(T){var A=0;T.fn.TouchSpin=function(N){var q={min:0,max:100,initval:"",replacementval:"",firstclickvalueifempty:null,step:1,decimals:0,stepinterval:100,forcestepdivisibility:"round",stepintervaldelay:500,verticalbuttons:!1,verticalup:"&plus;",verticaldown:"&minus;",verticalupclass:"",verticaldownclass:"",prefix:"",postfix:"",prefix_extraclass:"",postfix_extraclass:"",booster:!0,boostat:10,maxboostedstep:!1,mousewheel:!0,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary",buttondown_txt:"&minus;",buttonup_txt:"&plus;",callback_before_calculation:function(t){return t},callback_after_calculation:function(t){return t}},B={min:"min",max:"max",initval:"init-val",replacementval:"replacement-val",firstclickvalueifempty:"first-click-value-if-empty",step:"step",decimals:"decimals",stepinterval:"step-interval",verticalbuttons:"vertical-buttons",verticalupclass:"vertical-up-class",verticaldownclass:"vertical-down-class",forcestepdivisibility:"force-step-divisibility",stepintervaldelay:"step-interval-delay",prefix:"prefix",postfix:"postfix",prefix_extraclass:"prefix-extra-class",postfix_extraclass:"postfix-extra-class",booster:"booster",boostat:"boostat",maxboostedstep:"max-boosted-step",mousewheel:"mouse-wheel",buttondown_class:"button-down-class",buttonup_class:"button-up-class",buttondown_txt:"button-down-txt",buttonup_txt:"button-up-txt"};return this.each(function(){var a,e,s,i,t,o,n,p,u,c,r,l,d,f,b,h,m,v=T(this),x=v.data(),g=0,y=!1;function w(){""===a.prefix&&(e=i.prefix.detach()),""===a.postfix&&(s=i.postfix.detach())}function _(){var t,n,o=a.callback_before_calculation(v.val());""===o?""!==a.replacementval&&(v.val(a.replacementval),v.trigger("change")):0<a.decimals&&"."===o||(t=parseFloat(o),(n=t=isNaN(t)?""!==a.replacementval?a.replacementval:0:t).toString()!==o&&(n=t),n=function(t){switch(a.forcestepdivisibility){case"round":return(Math.round(t/a.step)*a.step).toFixed(a.decimals);case"floor":return(Math.floor(t/a.step)*a.step).toFixed(a.decimals);case"ceil":return(Math.ceil(t/a.step)*a.step).toFixed(a.decimals);default:return t.toFixed(a.decimals)}}(t),null!==a.min&&t<a.min&&(n=a.min),null!==a.max&&t>a.max&&(n=a.max),parseFloat(t).toString()!==parseFloat(n).toString()&&v.val(n),v.val(a.callback_after_calculation(parseFloat(n).toFixed(a.decimals))))}function k(){var t;return a.booster?(t=Math.pow(2,Math.floor(g/a.boostat))*a.step,a.maxboostedstep&&t>a.maxboostedstep&&(t=a.maxboostedstep,o=Math.round(o/t)*t),Math.max(a.step,t)):a.step}function C(){return"number"==typeof a.firstclickvalueifempty?a.firstclickvalueifempty:(a.min+a.max)/2}function F(){var t=v.is(":disabled,[readonly]");i.up.prop("disabled",t),i.down.prop("disabled",t),t&&P()}function j(){var t,n;v.is(":disabled,[readonly]")||(_(),t=o=parseFloat(a.callback_before_calculation(i.input.val())),isNaN(o)?o=C():(n=k(),o+=n),null!==a.max&&o>=a.max&&(o=a.max,v.trigger("touchspin.on.max"),P()),i.input.val(a.callback_after_calculation(parseFloat(o).toFixed(a.decimals))),t!==o&&v.trigger("change"))}function D(){var t,n;v.is(":disabled,[readonly]")||(_(),t=o=parseFloat(a.callback_before_calculation(i.input.val())),isNaN(o)?o=C():(n=k(),o-=n),null!==a.min&&o<=a.min&&(o=a.min,v.trigger("touchspin.on.min"),P()),i.input.val(a.callback_after_calculation(parseFloat(o).toFixed(a.decimals))),t!==o&&v.trigger("change"))}function M(){v.is(":disabled,[readonly]")||(P(),g=0,y="down",v.trigger("touchspin.on.startspin"),v.trigger("touchspin.on.startdownspin"),u=setTimeout(function(){n=setInterval(function(){g++,D()},a.stepinterval)},a.stepintervaldelay))}function S(){v.is(":disabled,[readonly]")||(P(),g=0,y="up",v.trigger("touchspin.on.startspin"),v.trigger("touchspin.on.startupspin"),c=setTimeout(function(){p=setInterval(function(){g++,j()},a.stepinterval)},a.stepintervaldelay))}function P(){switch(clearTimeout(u),clearTimeout(c),clearInterval(n),clearInterval(p),y){case"up":v.trigger("touchspin.on.stopupspin"),v.trigger("touchspin.on.stopspin");break;case"down":v.trigger("touchspin.on.stopdownspin"),v.trigger("touchspin.on.stopspin")}g=0,y=!1}v.data("alreadyinitialized")||(v.data("alreadyinitialized",!0),A+=1,v.data("spinnerid",A),v.is("input")?(a=T.extend({},q,x,function(){var o={};return T.each(B,function(t,n){n="bts-"+n;v.is("[data-"+n+"]")&&(o[t]=v.data(n))}),T.each(["min","max","step"],function(t,n){v.is("["+n+"]")&&(void 0!==o[n]&&console.warn('Both the "data-bts-'+n+'" data attribute and the "'+n+'" individual attribute were specified, the individual attribute will take precedence on: ',v),o[n]=v.attr(n))}),o}(),N),1!==parseFloat(a.step)&&(0!=(r=a.max%a.step)&&(a.max=parseFloat(a.max)-r),0!=(r=a.min%a.step))&&(a.min=parseFloat(a.min)+(parseFloat(a.step)-r)),""!==a.initval&&""===v.val()&&v.val(a.initval),_(),r=v.val(),x=v.parent(),""!==r&&(r=a.callback_before_calculation(r),r=a.callback_after_calculation(parseFloat(r).toFixed(a.decimals))),v.data("initvalue",r).val(r),v.addClass("form-control"),t='\n          <span class="input-group-addon bootstrap-touchspin-vertical-button-wrapper">\n            <span class="input-group-btn-vertical">\n              <button tabindex="-1" class="'.concat(a.buttondown_class," bootstrap-touchspin-up ").concat(a.verticalupclass,'" type="button">').concat(a.verticalup,'</button>\n              <button tabindex="-1" class="').concat(a.buttonup_class," bootstrap-touchspin-down ").concat(a.verticaldownclass,'" type="button">').concat(a.verticaldown,"</button>\n            </span>\n          </span>\n       "),x.hasClass("input-group")?((x=x).addClass("bootstrap-touchspin"),m=v.prev(),f=v.next(),b='\n            <span class="input-group-addon input-group-prepend bootstrap-touchspin-prefix input-group-prepend bootstrap-touchspin-injected">\n              <span class="input-group-text">'.concat(a.prefix,"</span>\n            </span>\n          "),h='\n            <span class="input-group-addon input-group-append bootstrap-touchspin-postfix input-group-append bootstrap-touchspin-injected">\n              <span class="input-group-text">'.concat(a.postfix,"</span>\n            </span>\n          "),a.verticalbuttons?T(t).insertAfter(v):(m.hasClass("input-group-btn")||m.hasClass("input-group-prepend")?(l='\n              <button tabindex="-1" class="'.concat(a.buttondown_class,' bootstrap-touchspin-down bootstrap-touchspin-injected" type="button">').concat(a.buttondown_txt,"</button>\n            "),m.append(l)):(l='\n              <span class="input-group-btn input-group-prepend bootstrap-touchspin-injected">\n                <button tabindex="-1" class="'.concat(a.buttondown_class,' bootstrap-touchspin-down" type="button">').concat(a.buttondown_txt,"</button>\n              </span>\n            "),T(l).insertBefore(v)),f.hasClass("input-group-btn")||f.hasClass("input-group-append")?(d='\n            <button tabindex="-1" class="'.concat(a.buttonup_class,' bootstrap-touchspin-up bootstrap-touchspin-injected" type="button">').concat(a.buttonup_txt,"</button>\n          "),f.prepend(d)):(d='\n            <span class="input-group-btn input-group-append bootstrap-touchspin-injected">\n              <button tabindex="-1" class="'.concat(a.buttonup_class,' bootstrap-touchspin-up" type="button">').concat(a.buttonup_txt,"</button>\n            </span>\n          "),T(d).insertAfter(v))),T(b).insertBefore(v),T(h).insertAfter(v),l=x):(m="",v.hasClass("input-sm")||v.hasClass("form-control-sm")?m="input-group-sm":(v.hasClass("input-lg")||v.hasClass("form-control-lg"))&&(m="input-group-lg"),m=a.verticalbuttons?'\n            <div class="input-group '.concat(m,' bootstrap-touchspin bootstrap-touchspin-injected">\n              <span class="input-group-addon input-group-prepend bootstrap-touchspin-prefix">\n                <span class="input-group-text">').concat(a.prefix,'</span>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-postfix input-group-append">\n                <span class="input-group-text">').concat(a.postfix,"</span>\n              </span>\n              ").concat(t,"\n            </div>\n          "):'\n            <div class="input-group bootstrap-touchspin bootstrap-touchspin-injected">\n              <span class="input-group-btn input-group-prepend">\n                <button tabindex="-1" class="'.concat(a.buttondown_class,' bootstrap-touchspin-down" type="button">').concat(a.buttondown_txt,'</button>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-prefix input-group-prepend">\n                <span class="input-group-text">').concat(a.prefix,'</span>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-postfix input-group-append">\n                <span class="input-group-text">').concat(a.postfix,'</span>\n              </span>\n              <span class="input-group-btn input-group-append">\n                <button tabindex="-1" class="').concat(a.buttonup_class,' bootstrap-touchspin-up" type="button">').concat(a.buttonup_txt,"</button>\n              </span>\n            </div>"),l=T(m).insertBefore(v),T(".bootstrap-touchspin-prefix",l).after(v),v.hasClass("input-sm")||v.hasClass("form-control-sm")?l.addClass("input-group-sm"):(v.hasClass("input-lg")||v.hasClass("form-control-lg"))&&l.addClass("input-group-lg")),i={down:T(".bootstrap-touchspin-down",l),up:T(".bootstrap-touchspin-up",l),input:T("input",l),prefix:T(".bootstrap-touchspin-prefix",l).addClass(a.prefix_extraclass),postfix:T(".bootstrap-touchspin-postfix",l).addClass(a.postfix_extraclass)},F(),w(),"undefined"!=typeof MutationObserver&&new MutationObserver(function(t){t.forEach(function(t){"attributes"!==t.type||"disabled"!==t.attributeName&&"readonly"!==t.attributeName||F()})}).observe(v[0],{attributes:!0}),v.on("keydown.touchspin",function(t){var n=t.keyCode||t.which;38===n?("up"!==y&&(j(),S()),t.preventDefault()):40===n?("down"!==y&&(D(),M()),t.preventDefault()):9!==n&&13!==n||_()}),v.on("keyup.touchspin",function(t){t=t.keyCode||t.which;38!==t&&40!==t||P()}),T(document).on("mousedown touchstart",function(t){T(t.target).is(v)||_()}),v.on("blur.touchspin",function(){_()}),i.down.on("keydown",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||("down"!==y&&(D(),M()),t.preventDefault())}),i.down.on("keyup.touchspin",function(t){t=t.keyCode||t.which;32!==t&&13!==t||P()}),i.up.on("keydown.touchspin",function(t){var n=t.keyCode||t.which;32!==n&&13!==n||("up"!==y&&(j(),S()),t.preventDefault())}),i.up.on("keyup.touchspin",function(t){t=t.keyCode||t.which;32!==t&&13!==t||P()}),i.down.on("mousedown.touchspin",function(t){i.down.off("touchstart.touchspin"),v.is(":disabled,[readonly]")||(D(),M(),t.preventDefault(),t.stopPropagation())}),i.down.on("touchstart.touchspin",function(t){i.down.off("mousedown.touchspin"),v.is(":disabled,[readonly]")||(D(),M(),t.preventDefault(),t.stopPropagation())}),i.up.on("mousedown.touchspin",function(t){i.up.off("touchstart.touchspin"),v.is(":disabled,[readonly]")||(j(),S(),t.preventDefault(),t.stopPropagation())}),i.up.on("touchstart.touchspin",function(t){i.up.off("mousedown.touchspin"),v.is(":disabled,[readonly]")||(j(),S(),t.preventDefault(),t.stopPropagation())}),i.up.on("mouseup.touchspin mouseout.touchspin touchleave.touchspin touchend.touchspin touchcancel.touchspin",function(t){y&&(t.stopPropagation(),P())}),i.down.on("mouseup.touchspin mouseout.touchspin touchleave.touchspin touchend.touchspin touchcancel.touchspin",function(t){y&&(t.stopPropagation(),P())}),i.down.on("mousemove.touchspin touchmove.touchspin",function(t){y&&(t.stopPropagation(),t.preventDefault())}),i.up.on("mousemove.touchspin touchmove.touchspin",function(t){y&&(t.stopPropagation(),t.preventDefault())}),v.on("mousewheel.touchspin DOMMouseScroll.touchspin",function(t){var n;a.mousewheel&&v.is(":focus")&&(n=t.originalEvent.wheelDelta||-t.originalEvent.deltaY||-t.originalEvent.detail,t.stopPropagation(),t.preventDefault(),(n<0?D:j)())}),v.on("touchspin.destroy",function(){var t=v.parent();P(),v.off(".touchspin"),t.hasClass("bootstrap-touchspin-injected")?(v.siblings().remove(),v.unwrap()):(T(".bootstrap-touchspin-injected",t).remove(),t.removeClass("bootstrap-touchspin")),v.data("alreadyinitialized",!1)}),v.on("touchspin.uponce",function(){P(),j()}),v.on("touchspin.downonce",function(){P(),D()}),v.on("touchspin.startupspin",function(){S()}),v.on("touchspin.startdownspin",function(){M()}),v.on("touchspin.stopspin",function(){P()}),v.on("touchspin.updatesettings",function(t,n){var o=n;a=T.extend({},a,o),o.postfix&&(0===v.parent().find(".bootstrap-touchspin-postfix").length&&s.insertAfter(v),v.parent().find(".bootstrap-touchspin-postfix .input-group-text").text(o.postfix)),o.prefix&&(0===v.parent().find(".bootstrap-touchspin-prefix").length&&e.insertBefore(v),v.parent().find(".bootstrap-touchspin-prefix .input-group-text").text(o.prefix)),w(),_(),""!==(n=i.input.val())&&(n=parseFloat(a.callback_before_calculation(i.input.val())),i.input.val(a.callback_after_calculation(parseFloat(n).toFixed(a.decimals))))})):console.log("Must be an input."))})}});