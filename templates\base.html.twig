<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta content="{{ app_name }}" name="keywords">
    <meta content="{{ app_name }}" name="description">
    <meta content="{{ app_name }}" name="author">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>{% block title %}Welcome!{% endblock %} | {{ app_name }} - High-performance and fully flexible automated
        e-commerce platform</title>

    <link rel="shortcut icon" href="{{ asset('resources/theme/images/favicon.ico') }}">

    {% block importmap %}{{ importmap('app') }}{% endblock %}

    {% block stylesheets %}
        <link rel="stylesheet" type="text/css" href="{{ asset('resources/theme/css/bootstrap.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ asset('resources/theme/css/icons.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ asset('resources/theme/css/app.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ asset('resources/theme/css/customizer.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ asset('resources/theme/css/tabulator.css') }}">
    {% endblock %}
</head>
<body {% block body_customized %}{% endblock %}>

<div id="x-body">
    {% block body %}{% endblock %}
</div>

{% block javascripts %}
    <script src="{{ asset('resources/theme/plugins/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('resources/theme/plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('resources/theme/plugins/metismenu/metisMenu.min.js') }}"></script>
    <script src="{{ asset('resources/theme/plugins/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('resources/theme/plugins/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('resources/theme/js/app.js') }}"></script>
{% endblock %}

<script>
    $(document).ready(function () {
        const currentUrl = new URL(window.location.href)
        const currentPath = currentUrl.origin + currentUrl.pathname

        $('.x-nav-link').each(function () {
            const $link = $(this)
            const linkPath = $link.data('url')

            try {
                const linkUrl = new URL(linkPath, window.location.origin)
                const linkCleanPath = linkUrl.origin + linkUrl.pathname

                if (linkCleanPath === currentPath) {
                    $link.addClass('active')
                    $link.closest('li').addClass('mm-active')
                }
            } catch (e) {
                console.warn('Invalid data-url:', linkPath)
            }
        })
    })
</script>

{% if captchaWidget is defined %}
    {{ captchaWidget | raw }}
{% endif %}

</body>
</html>
