{% extends 'domains/admin/shared/layouts/main.html.twig' %}

{% block title %}Create New Account{% endblock %}

{% block content %}
    <div class="col-12">
        <div class="d-flex align-items-center py-5">
            <h2 class="h4 mb-0 me-3">
                Create New Account
            </h2>
            <div class="d-none d-sm-flex">
                <div class="vr"></div>
            </div>
            {{ breadcrumb(
                [
                    {'name': 'Account Management', 'url': path('app_admin_account_list_index'), 'sidebar': 'management'},
                    {'name': 'Create New Account', 'url': path('app_admin_account_management_create'), 'sidebar': 'management'}
                ]
            ) }}
        </div>

        {{ form_start(form, {'attr': {'id': 'create-account-form', 'class': 'needs-validation', 'data-turbo': 'true', 'novalidate': ''}}) }}
        <div class="card">
            <div class="card-header d-flex flex-column flex-sm-row align-items-center justify-content-between">
                <h2 class="h5 mb-0">
                    Account Information
                </h2>
            </div>
            <div class="card-body">
                {% for label, messages in app.flashes %}
                    {% for message in messages %}
                        <div class="alert {{ label == 'success' ? 'alert-success' : 'alert-danger' }} alert-dismissible fade show mb-3" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fa {{ label == 'success' ? 'fa-check' : 'fa-exclamation-triangle' }} me-2"></i>
                                <span>{{ message | raw }}</span>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endfor %}

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.firstName.vars.id }}">
                            {{ form_label(form.firstName) }}
                        </label>
                        {{ form_widget(form.firstName, {'attr': {'class': 'form-control'}}) }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.lastName.vars.id }}">
                            {{ form_label(form.lastName) }}
                        </label>
                        {{ form_widget(form.lastName, {'attr': {'class': 'form-control'}}) }}
                    </div>
                </div>

                <hr class="my-4">

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.userName.vars.id }}">
                            {{ form_label(form.userName) }}
                        </label>
                        {{ form_widget(form.userName, {'attr': {'class': 'form-control'}}) }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.email.vars.id }}">
                            {{ form_label(form.email) }}
                        </label>
                        {{ form_widget(form.email, {'attr': {'class': 'form-control'}}) }}
                    </div>
                </div>

                <hr class="my-4">

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.password.vars.id }}">
                            {{ form_label(form.password) }}
                        </label>
                        {{ form_widget(form.password, {'attr': {'class': 'form-control'}}) }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="registration_ip">
                            Registration IP
                        </label>
                        <input type="text" id="registration_ip" value="{{ registration_ip }}" readonly
                               class="form-control" style="background-color: #f8f9fa;"/>
                    </div>
                </div>

                <hr class="my-4">

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.role.vars.id }}">
                            {{ form_label(form.role) }}
                        </label>
                        {{ form_widget(form.role, {'attr': {'class': 'form-select'}}) }}
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="{{ form.status.vars.id }}">
                            {{ form_label(form.status) }}
                        </label>
                        {{ form_widget(form.status, {'attr': {'class': 'form-select'}}) }}
                    </div>
                </div>

                <div class="d-flex justify-content-center gap-2 pt-4">
                    <button type="button" onclick="window.location.href='{{ path('app_admin_account_list_index') }}'"
                            class="btn btn-outline-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        Save
                    </button>
                </div>
            </div>
        </div>
        {{ form_end(form) }}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        (function () {
            function preventSpace (e) {
                if (e.key === ' ' || e.keyCode === 32) {
                    e.preventDefault()
                }
            }

            function removeSpaces (input) {
                if (input?.value?.includes(' ')) {
                    input.value = input.value.replace(/\s/g, '')
                }
            }

            function handlePaste (e) {
                e.preventDefault()
                this.value = (e.clipboardData || window.clipboardData).getData('text').replace(/\s/g, '')
            }

            function setupNoSpaceInputs () {
                const inputs = [
                    document.getElementById('{{ form.userName.vars.id }}'),
                    document.getElementById('{{ form.email.vars.id }}'),
                ]

                inputs.forEach(input => {
                    if (!input) {
                        return
                    }

                    removeSpaces(input)

                    input.removeEventListener('keydown', preventSpace)
                    input.removeEventListener('input', () => removeSpaces(input))
                    input.removeEventListener('paste', handlePaste)

                    input.addEventListener('keydown', preventSpace)
                    input.addEventListener('input', () => removeSpaces(input))
                    input.addEventListener('paste', handlePaste)
                })
            }

            setupNoSpaceInputs()
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', setupNoSpaceInputs)
            }
            [0, 100, 500].forEach(ms => setTimeout(setupNoSpaceInputs, ms))
            window.addEventListener('load', setupNoSpaceInputs)

            new MutationObserver(setupNoSpaceInputs).observe(document.body, {
                childList: true,
                subtree: true,
            })

            const form = document.getElementById('create-account-form')
            form?.addEventListener('submit', () => {
                [
                    document.getElementById('{{ form.userName.vars.id }}'),
                    document.getElementById('{{ form.email.vars.id }}'),
                ].forEach(input => input && removeSpaces(input))
            })
        })()
    </script>
{% endblock %}
