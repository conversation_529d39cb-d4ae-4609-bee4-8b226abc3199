<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Parser;

use App\Library\Tabulator\Base\AbstractColumn;

/**
 * Interface ParserInterface.
 *
 * Defines the contract for classes that parse or transform data from an adapter query.
 * Implementations should process raw adapter data and format or transform it based on provided columns.
 */
interface ParserInterface
{

    /**
     * Parse data.
     *
     * @param  array             $adapterData
     * @param  AbstractColumn[]  $columns
     *
     * @return array
     */
    public function parse(array $adapterData, array $columns): array;

}
