<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class TickCrossColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined tick-cross-column function.
 */
class TickCrossColumn extends AbstractColumn
{

    protected function getDefaultConfig(): array
    {
        return [
            'formatter'       => 'tickCross',
            'formatterParams' => [
                'tickElement'  => $this->getOption('tickElement'),
                'crossElement' => $this->getOption('crossElement'),
            ],
        ];
    }

    protected function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver
            ->setRequired(['tickElement', 'crossElement'])
            ->setDefaults([
                'extra' => [
                    'formatterParams' => [
                        'allowEmpty'  => false,
                        'allowTruthy' => false,
                    ],
                ],
            ])
            ->setAllowedTypes('tickElement', 'string')
            ->setAllowedTypes('crossElement', 'string');
    }

    public function prepareContent($value): mixed
    {
        return $value;
    }

}
