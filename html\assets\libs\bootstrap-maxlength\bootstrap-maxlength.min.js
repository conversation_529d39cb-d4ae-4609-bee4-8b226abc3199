!function($){"use strict";$.event.special.destroyed||($.event.special.destroyed={remove:function(o){o.handler&&o.handler()}}),$.fn.extend({maxlength:function(options,callback){var documentBody=$("body");function utf8CharByteCount(character){var c=character.charCodeAt();return c?c<128?1:c<2048?2:3:0}function utf8Length(string){return string.split("").map(utf8CharByteCount).concat(0).reduce((function(sum,val){return sum+val}))}function inputLength(input){var text=input.val();text=options.twoCharLinebreak?text.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"):text.replace(/(?:\r\n|\r|\n)/g,"\n");var currentLength=0;return currentLength=options.utf8?utf8Length(text):text.length,"file"===input.prop("type")&&""!==input.val()&&(currentLength-=12),currentLength}function remainingChars(input,maxlength){return maxlength-inputLength(input)}function showRemaining(currentInput,indicator){indicator.css({display:"block"}),currentInput.trigger("maxlength.shown")}function updateMaxLengthHTML(currentInputText,maxLengthThisInput,typedChars){var output="";return options.message?output="function"==typeof options.message?options.message(currentInputText,maxLengthThisInput):options.message.replace("%charsTyped%",typedChars).replace("%charsRemaining%",maxLengthThisInput-typedChars).replace("%charsTotal%",maxLengthThisInput):(options.preText&&(output+=options.preText),options.showCharsTyped?output+=typedChars:output+=maxLengthThisInput-typedChars,options.showMaxLength&&(output+=options.separator+maxLengthThisInput),options.postText&&(output+=options.postText)),output}function manageRemainingVisibility(remaining,currentInput,maxLengthCurrentInput,maxLengthIndicator){var input,threshold,maxlength,output;maxLengthIndicator&&(maxLengthIndicator.html(updateMaxLengthHTML(currentInput.val(),maxLengthCurrentInput,maxLengthCurrentInput-remaining)),remaining>0?(input=currentInput,threshold=options.threshold,maxlength=maxLengthCurrentInput,output=!0,!options.alwaysShow&&maxlength-inputLength(input)>threshold&&(output=!1),output?showRemaining(currentInput,maxLengthIndicator.removeClass(options.limitReachedClass+" "+options.limitExceededClass).addClass(options.warningClass)):function(currentInput,indicator){options.alwaysShow||(indicator.css({display:"none"}),currentInput.trigger("maxlength.hidden"))}(currentInput,maxLengthIndicator)):options.limitExceededClass?showRemaining(currentInput,0===remaining?maxLengthIndicator.removeClass(options.warningClass+" "+options.limitExceededClass).addClass(options.limitReachedClass):maxLengthIndicator.removeClass(options.warningClass+" "+options.limitReachedClass).addClass(options.limitExceededClass)):showRemaining(currentInput,maxLengthIndicator.removeClass(options.warningClass).addClass(options.limitReachedClass))),options.customMaxAttribute&&(remaining<0?currentInput.addClass(options.customMaxClass):currentInput.removeClass(options.customMaxClass))}function place(currentInput,maxLengthIndicator){var pos=function(currentInput){var el=currentInput[0];return $.extend({},"function"==typeof el.getBoundingClientRect?el.getBoundingClientRect():{width:el.offsetWidth,height:el.offsetHeight},currentInput.offset())}(currentInput);if("function"!==$.type(options.placement))if($.isPlainObject(options.placement))!function(placement,maxLengthIndicator){if(placement&&maxLengthIndicator){var cssPos={};$.each(["top","bottom","left","right","position"],(function(i,key){var val=options.placement[key];void 0!==val&&(cssPos[key]=val)})),maxLengthIndicator.css(cssPos)}}(options.placement,maxLengthIndicator);else{var inputOuter=currentInput.outerWidth(),outerWidth=maxLengthIndicator.outerWidth(),actualWidth=maxLengthIndicator.width(),actualHeight=maxLengthIndicator.height();switch(options.appendToParent&&(pos.top-=currentInput.parent().offset().top,pos.left-=currentInput.parent().offset().left),options.placement){case"bottom":maxLengthIndicator.css({top:pos.top+pos.height,left:pos.left+pos.width/2-actualWidth/2});break;case"top":maxLengthIndicator.css({top:pos.top-actualHeight,left:pos.left+pos.width/2-actualWidth/2});break;case"left":maxLengthIndicator.css({top:pos.top+pos.height/2-actualHeight/2,left:pos.left-actualWidth});break;case"right":maxLengthIndicator.css({top:pos.top+pos.height/2-actualHeight/2,left:pos.left+pos.width});break;case"bottom-right":maxLengthIndicator.css({top:pos.top+pos.height,left:pos.left+pos.width});break;case"top-right":maxLengthIndicator.css({top:pos.top-actualHeight,left:pos.left+inputOuter});break;case"top-left":maxLengthIndicator.css({top:pos.top-actualHeight,left:pos.left-outerWidth});break;case"bottom-left":maxLengthIndicator.css({top:pos.top+currentInput.outerHeight(),left:pos.left-outerWidth});break;case"centered-right":maxLengthIndicator.css({top:pos.top+actualHeight/2,left:pos.left+inputOuter-outerWidth-3});break;case"bottom-right-inside":maxLengthIndicator.css({top:pos.top+pos.height,left:pos.left+pos.width-outerWidth});break;case"top-right-inside":maxLengthIndicator.css({top:pos.top-actualHeight,left:pos.left+inputOuter-outerWidth});break;case"top-left-inside":maxLengthIndicator.css({top:pos.top-actualHeight,left:pos.left});break;case"bottom-left-inside":maxLengthIndicator.css({top:pos.top+currentInput.outerHeight(),left:pos.left})}}else options.placement(currentInput,maxLengthIndicator,pos)}function getMaxLength(currentInput){var max=currentInput.attr("maxlength")||options.customMaxAttribute;if(options.customMaxAttribute&&!options.allowOverMax){var custom=currentInput.attr(options.customMaxAttribute);(!max||custom<max)&&(max=custom)}return max||(max=currentInput.attr("size")),max}return $.isFunction(options)&&!callback&&(callback=options,options={}),options=$.extend({showOnReady:!1,alwaysShow:!0,threshold:0,warningClass:"small form-text text-muted",limitReachedClass:"small form-text text-danger",limitExceededClass:"",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom-right-inside",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,customMaxAttribute:null,customMaxClass:"overmax",allowOverMax:!1,zIndex:1099},options),this.each((function(){var maxLengthCurrentInput,maxLengthIndicator,currentInput=$(this);function firstInit(){var maxlengthContent=updateMaxLengthHTML(currentInput.val(),maxLengthCurrentInput,"0");maxLengthCurrentInput=getMaxLength(currentInput),maxLengthIndicator||(maxLengthIndicator=$('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:options.zIndex}).html(maxlengthContent)),currentInput.is("textarea")&&(currentInput.data("maxlenghtsizex",currentInput.outerWidth()),currentInput.data("maxlenghtsizey",currentInput.outerHeight()),currentInput.mouseup((function(){currentInput.outerWidth()===currentInput.data("maxlenghtsizex")&&currentInput.outerHeight()===currentInput.data("maxlenghtsizey")||place(currentInput,maxLengthIndicator),currentInput.data("maxlenghtsizex",currentInput.outerWidth()),currentInput.data("maxlenghtsizey",currentInput.outerHeight())}))),options.appendToParent?(currentInput.parent().append(maxLengthIndicator),currentInput.parent().css("position","relative")):documentBody.append(maxLengthIndicator),manageRemainingVisibility(remainingChars(currentInput,getMaxLength(currentInput)),currentInput,maxLengthCurrentInput,maxLengthIndicator),place(currentInput,maxLengthIndicator)}$(window).resize((function(){maxLengthIndicator&&place(currentInput,maxLengthIndicator)})),options.showOnReady?currentInput.ready((function(){firstInit()})):currentInput.focus((function(){firstInit()})),currentInput.on("maxlength.reposition",(function(){place(currentInput,maxLengthIndicator)})),currentInput.on("destroyed",(function(){maxLengthIndicator&&maxLengthIndicator.remove()})),currentInput.on("blur",(function(){maxLengthIndicator&&!options.showOnReady&&maxLengthIndicator.remove()})),currentInput.on("input",(function(){var maxlength=getMaxLength(currentInput),remaining=remainingChars(currentInput,maxlength),output=!0;return options.validate&&remaining<0?(!function(input,maxlength){var text=input.val();if(options.twoCharLinebreak&&"\n"===(text=text.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"))[text.length-1]&&(maxlength-=text.length%2),options.utf8){for(var indexedSize=text.split("").map(utf8CharByteCount),removedBytes=0,bytesPastMax=utf8Length(text)-maxlength;removedBytes<bytesPastMax;removedBytes+=indexedSize.pop());maxlength-=maxlength-indexedSize.length}input.val(text.substr(0,maxlength))}(currentInput,maxlength),output=!1):manageRemainingVisibility(remaining,currentInput,maxLengthCurrentInput,maxLengthIndicator),output}))}))}})}(jQuery);