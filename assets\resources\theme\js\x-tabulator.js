const initTabulator = (Tabulator, tableConfig) => {
    return new Promise((resolve, reject) => {
        let table = new Tabulator(tableConfig.selector, tableConfig.options)

        if (table.options['ajaxContentType'] === 'json') {
            table.options['ajaxContentType'] = {
                headers: {
                    'Content-Type': 'application/json', 'X-Request-Generator': 'tabulator',
                }, body: function (url, config, params) {
                    return JSON.stringify(params)
                },
            }
        }

        table.on('tableBuilt', () => {
            resolve(table)
        })
    })
}

export { initTabulator }
