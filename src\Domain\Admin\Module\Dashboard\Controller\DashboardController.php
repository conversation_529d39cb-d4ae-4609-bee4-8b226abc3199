<?php

declare(strict_types=1);

namespace App\Domain\Admin\Module\Dashboard\Controller;

use App\Domain\Admin\Base\AdminController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DashboardController.
 *
 * This is the default controller of this module, handles the admin-dashboard and related functionalities.
 */
#[Route('/admin', name: 'app_admin_dashboard_')]
class DashboardController extends AdminController
{

    /**
     * Action `index`.
     *
     * This is the default action of this module, handles the admin-dashboard request.
     */
    #[Route(name: 'index', methods: ['GET'])]
    public function index(): Response
    {
        flash()->success('Welcome to ZenShop Admin!', [], 'Welcome');

        return $this->view('dashboard/pages/index');
    }

}
