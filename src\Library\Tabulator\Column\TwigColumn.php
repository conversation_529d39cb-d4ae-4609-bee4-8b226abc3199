<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;
use InvalidArgumentException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

/**
 * Class TwigColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined twig-column function.
 */
class TwigColumn extends AbstractColumn
{

    private ?Environment $environment = null;

    public function __construct(?Environment $environment = null)
    {
        if (null === $environment) {
            throw new InvalidArgumentException('Install symfony/twig-bundle to use the TwigColumn');
        }

        $this->environment = $environment;
    }

    protected function getDefaultConfig(): array
    {
        return ['formatter' => 'html'];
    }

    protected function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver->setRequired(['template'])
            ->setDefaults(['passRow' => false])
            ->setAllowedTypes('template', 'string')
            ->setAllowedTypes('passRow', 'bool');
    }

    /**
     * @inheritDoc
     *
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function prepareContent($value): string
    {
        return $this->environment->render($this->getOption('template'), ['content' => $value]);
    }

}
