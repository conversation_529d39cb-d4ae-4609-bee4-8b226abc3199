<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Adapter;

use App\Library\Tabulator\ArrayResult;
use App\Library\Tabulator\Base\AbstractAdapter;
use App\Library\Tabulator\Enum\FilteringComparison;
use App\Library\Tabulator\Enum\FilteringType;
use App\Library\Tabulator\Enum\SortingDirection;
use App\Library\Tabulator\ResultInterface;
use InvalidArgumentException;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class ArrayAdapter.
 *
 * Implements the AbstractAdapter to handle data retrieval, filtering, sorting, and pagination
 * using an in-memory array. This class provides a mechanism to filter, sort, and paginate array data based on the
 * given AdapterQuery.
 */
class ArrayAdapter extends AbstractAdapter
{

    public function getData(QueryAdapter $adapterQuery): ResultInterface
    {
        $adapterData = $this->getOption('data');

        if ($adapterQuery->getFilteringBag()->hasFiltering()) {
            $adapterData = array_filter($adapterData, function (array $item) use ($adapterQuery) {
                $filterAndResult = $this->getFilterAndResult($adapterQuery, $item);
                $filterOrResult = $this->getFilterOrResult($adapterQuery, $item);

                return $filterAndResult && $filterOrResult;
            });
        }

        if ($adapterQuery->getSortingBag()->hasSorting()) {
            foreach ($adapterQuery->getSortingBag()->getSortingItems() as $sortingItem) {
                $sortingItem->getDirection() === SortingDirection::ASC ? sort($adapterData) : rsort($adapterData);
            }
        }

        if ($adapterQuery->isPagination()) {
            $totalRecords = count($adapterData);
            $totalPages = ceil($totalRecords / $adapterQuery->getPaginationSize());
            $data = array_slice(
                $adapterData,
                ($adapterQuery->getPaginationPage() * $adapterQuery->getPaginationSize())
                - $adapterQuery->getPaginationSize(),
                $adapterQuery->getPaginationSize()
            );

            return new ArrayResult($data, (int)$totalPages, $totalRecords);
        }

        return new ArrayResult($adapterData);
    }

    protected function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setRequired(["data"])->setAllowedTypes('data', 'array');
    }

    /**
     * Retrieves data with filter and result.
     *
     * @param  QueryAdapter  $adapterQuery
     * @param  array         $item
     *
     * @return true|int|string
     */
    private function getFilterAndResult(QueryAdapter $adapterQuery, array $item): true|int|string
    {
        $filterAndResult = true;

        foreach ($adapterQuery->getFilteringBag()->getFilters(FilteringComparison::AND) as $filterItem) {
            if ($filterItem->getType() === FilteringType::EQUAL && $filterItem->getColumn() !== null) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] === $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::NOT_EQUAL) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] !== $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::GREATER) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] > $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::GREATER_OR_EQUAL) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] >= $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::LESS) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] < $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::LESS_OR_EQUAL) {
                $filterAndResult &= $item[$filterItem->getColumn()->getOption('field')] <= $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::STARTS_WITH) {
                $filterAndResult &= str_starts_with(
                    $item[$filterItem->getColumn()->getOption('field')],
                    $filterItem->getValue()
                );
            } elseif ($filterItem->getType() === FilteringType::ENDS_WITH) {
                $filterAndResult &= str_ends_with(
                    $item[$filterItem->getColumn()->getOption('field')],
                    $filterItem->getValue()
                );
            } elseif ($filterItem->getType() === FilteringType::LIKE) {
                $filterAndResult &= str_contains(
                    $item[$filterItem->getColumn()->getOption("field")],
                    $filterItem->getValue()
                );
            } else {
                throw new InvalidArgumentException('Provided filtering type is not supported by the ArrayAdapter');
            }
        }

        return $filterAndResult;
    }

    /**
     * Retrieves data with filter or result.
     *
     * @param  QueryAdapter  $adapterQuery
     * @param  array         $item
     *
     * @return bool|int|string
     */
    private function getFilterOrResult(QueryAdapter $adapterQuery, array $item): bool|int|string
    {
        $filterOrResult = count($adapterQuery->getFilteringBag()->getFilters(FilteringComparison::OR)) <= 0;

        foreach ($adapterQuery->getFilteringBag()->getFilters(FilteringComparison::OR) as $filterItem) {
            if ($filterItem->getType() === FilteringType::EQUAL && $filterItem->getColumn() !== null) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] === $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::NOT_EQUAL) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] !== $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::GREATER) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] > $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::GREATER_OR_EQUAL) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] >= $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::LESS) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] < $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::LESS_OR_EQUAL) {
                $filterOrResult |= $item[$filterItem->getColumn()->getOption('field')] <= $filterItem->getValue();
            } elseif ($filterItem->getType() === FilteringType::STARTS_WITH) {
                $filterOrResult |= str_starts_with(
                    $item[$filterItem->getColumn()->getOption('field')],
                    $filterItem->getValue()
                );
            } elseif ($filterItem->getType() === FilteringType::ENDS_WITH) {
                $filterOrResult |= str_ends_with(
                    $item[$filterItem->getColumn()->getOption('field')],
                    $filterItem->getValue()
                );
            } elseif ($filterItem->getType() === FilteringType::LIKE) {
                $filterOrResult |= str_contains(
                    $item[$filterItem->getColumn()->getOption('field')],
                    $filterItem->getValue()
                );
            } else {
                throw new InvalidArgumentException('Provided filtering type is not supported by the ArrayAdapter');
            }
        }

        return $filterOrResult;
    }

}
