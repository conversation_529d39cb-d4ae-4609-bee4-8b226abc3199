<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;

/**
 * Class TextColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined text-column function.
 */
class TextColumn extends AbstractColumn
{

    public function prepareContent($value): mixed
    {
        return $value;
    }

}
