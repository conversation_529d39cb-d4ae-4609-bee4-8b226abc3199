<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;
use DateTime;
use InvalidArgumentException;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class DateTimeColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined date-time function.
 */
class DateTimeColumn extends AbstractColumn
{

    protected function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver
            ->setRequired('format')
            ->setAllowedTypes('format', 'string');
    }

    public function prepareContent($value): mixed
    {
        if (!$value instanceof DateTime) {
            throw new InvalidArgumentException('Value must be a DateTime object');
        }

        return $value->format($this->getOption('format'));
    }

}
