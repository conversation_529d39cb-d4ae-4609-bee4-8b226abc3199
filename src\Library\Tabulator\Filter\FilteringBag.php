<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Filter;

use App\Library\Tabulator\Enum\FilteringComparison;

/**
 * Class FilteringBag.
 *
 * This class is responsible for managing and storing filter conditions.
 * It provides methods to add and retrieve filters, allowing for flexible filtering logic.
 */
class FilteringBag
{

    private array $filters
        = [
            FilteringComparison::AND->value => [],
            FilteringComparison::OR->value  => [],
        ];

    /**
     * Add filter.
     *
     * @param  FilteringItem        $item
     * @param  FilteringComparison  $comparison
     *
     * @return void
     */
    public function addFilter(FilteringItem $item, FilteringComparison $comparison): void
    {
        $this->filters[$comparison->value][] = $item;
    }

    /**
     * Retrieves filter.
     *
     * @param  FilteringComparison|null  $comparison
     *
     * @return FilteringItem[]|FilteringItem[][]
     */
    public function getFilters(?FilteringComparison $comparison = null): array
    {
        return null !== $comparison ? $this->filters[$comparison->value] : $this->filters;
    }

    /**
     * Check if there are any active filtering conditions.
     *
     * @return bool
     */
    public function hasFiltering(): bool
    {
        return count($this->filters[FilteringComparison::AND->value]) > 0
            || count(
                $this->filters[FilteringComparison::OR->value]
            ) > 0;
    }

}
