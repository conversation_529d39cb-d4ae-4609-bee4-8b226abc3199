<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Enum;

/**
 * Enum class FilteringType.
 *
 * This enum defines the possible comparison operators for filtering conditions.
 * It provides various types of comparisons that can be used to filter data based on specific
 * criteria. The operators range from simple equality checks to more complex pattern matching and range comparisons.
 */
enum FilteringType: string
{

    case EQUAL = '=';
    case NOT_EQUAL = '!=';
    case LIKE = 'like';
    case STARTS_WITH = 'starts';
    case ENDS_WITH = 'ends';
    case LESS = '<';
    case LESS_OR_EQUAL = '<=';
    case GREATER = '>';
    case GREATER_OR_EQUAL = '>=';
    case IN = 'in';
    case REGEX = 'regex';
    case KEYWORDS = 'keywords';

}
