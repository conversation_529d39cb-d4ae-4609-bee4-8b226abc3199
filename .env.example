###> App ###
APP_ENV=dev
APP_SECRET=02772d57b8ea794ea914273687cfcdev
APP_NAME="ZenShop"
APP_PORTAL_BASE_URL="http://zenshop.local:8081"
APP_ADMIN_BASE_URL="http://zenshop.local:8081/admin"
APP_CLIENT_BASE_URL="http://zenshop.local:8081/client"
APP_RESELLER_BASE_URL="http://zenshop.local:8081/reseller"
APP_PARTNER_BASE_URL="http://zenshop.local:8081/partner"
###< App ###

###> Docker ###
DOCKER_SQL_PORT=3307
DOCKER_NGINX_PORT=8081
DOCKER_NGINX_SSL_PORT=443
DOCKER_WEB_HOST="zenshop.local"
DOCKER_REDIS_PORT=6380
###< Docker ###

###> Database ###
DB_HOST=zenshop_mariadb
DB_ROOT_PASSWORD=root.zenshop@2025!
DB_PORT=3306
DB_NAME=zenshop_db
DB_USER=zenshop_mysql
DB_PASSWORD=mysql.zenshop@2025!

# Do not change this!
DATABASE_URL="mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?serverVersion=11.5.2-MariaDB&charset=utf8mb4"
###< Database ###

###> Messenger ###
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< Messenger ###

###> Mailer ###
MAILER_DSN=null://null
MAILER_SENDER_EMAIL=
MAILER_SENDER_NAME=
###< Mailer ###

###> hwi/oauth-bundle ###
FB_ID=
FB_SECRET=
###< hwi/oauth-bundle ###

###> google/recaptcha ###
GOOGLE_RECAPTCHA_SITE_KEY=
GOOGLE_RECAPTCHA_SECRET=
GOOGLE_RECAPTCHA_THRESHOLD=0.5
###< google/recaptcha ###

###> symfony/lock ###
LOCK_DSN=flock
###< symfony/lock ###

###> Redis ###
REDIS_HOST=zenshop_redis
REDIS_PORT=6379
REDIS_PASSWORD=redis.zenshop%402025!
REDIS_URL=redis://zenshop_redis:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}
###< Redis ###
