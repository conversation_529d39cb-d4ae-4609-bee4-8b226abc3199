<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Adapter;

use App\Library\Tabulator\Base\AbstractAdapter;
use App\Library\Tabulator\ResultInterface;
use LogicException;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class CallableAdapter.
 *
 * An adapter that allows fetching data by calling a user-defined callback function.
 */
class CallableAdapter extends AbstractAdapter
{

    public function getData(QueryAdapter $adapterQuery): ResultInterface
    {
        $functionResult = $this->getOption('function')($adapterQuery);

        if (!$functionResult instanceof ResultInterface) {
            throw new LogicException('The callable function must return an instance of ArrayResult');
        }

        return $functionResult;
    }

    protected function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setRequired(['function'])
            ->setAllowedTypes('function', 'callable');
    }

}
