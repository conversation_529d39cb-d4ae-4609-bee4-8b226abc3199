<?php

declare(strict_types=1);

namespace App\Library\Tabulator;

use App\Library\Tabulator\Adapter\QueryAdapter;
use App\Library\Tabulator\Base\AbstractAdapter;
use App\Library\Tabulator\Base\AbstractColumn;
use App\Library\Tabulator\Enum\FilteringComparison;
use App\Library\Tabulator\Enum\FilteringType;
use App\Library\Tabulator\Enum\SortingDirection;
use App\Library\Tabulator\Filter\FilteringItem;
use App\Library\Tabulator\Parser\ParserInterface;
use App\Library\Tabulator\Sorter\SortingItem;
use InvalidArgumentException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class Tabulator.
 *
 * This class represents a Tabulator instance used for managing dynamic table configurations,
 * including columns, sorting, filtering, and pagination. It provides methods to configure the table,
 * handle AJAX requests, and parse the data to be displayed in the table.
 *
 * The `Tabulator` class interacts with an adapter to fetch and process data, which can then be rendered
 * in a JavaScript-based table using the Tabulator library.
 */
class Tabulator
{

    private array $options = [];

    private array $columns = [];

    private ?AbstractAdapter $adapter = null;

    public function __construct(
        private string $selector,
        private readonly ?Request $request,
        private readonly InstanceStorage $instanceStorage,
        private readonly ParserInterface $parser,
    ) {
    }

    /**
     * Retrieves the selector.
     *
     * @return string
     */
    public function getSelector(): string
    {
        return $this->selector;
    }

    /**
     * Set the selector.
     *
     * @param  string  $selector
     *
     * @return $this
     */
    public function setSelector(string $selector): static
    {
        $this->selector = $selector;

        return $this;
    }

    /**
     * Retrieves the option array.
     *
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Retrieves an option by the given name.
     *
     * @param  string  $name
     *
     * @return mixed
     */
    public function getOption(string $name): mixed
    {
        if (!array_key_exists($name, $this->options)) {
            throw new InvalidArgumentException("Option '$name' does not exist");
        }

        return $this->options[$name];
    }

    /**
     * Set options.
     *
     * @param  array  $options
     *
     * @return $this
     */
    public function setOptions(array $options): static
    {
        $resolver = new OptionsResolver();
        $this->configureOptions($resolver);
        $this->options = $resolver->resolve($options);

        return $this;
    }

    /**
     * Retrieves all columns.
     *
     * @return array
     */
    public function getColumns(): array
    {
        return $this->columns;
    }

    /**
     * Retrieves a column by the given name.
     *
     * @param  string  $name
     *
     * @return AbstractColumn
     */
    public function getColumn(string $name): AbstractColumn
    {
        if (!array_key_exists($name, $this->columns)) {
            throw new InvalidArgumentException("Column '$name' does not exist");
        }

        return $this->columns[$name];
    }

    /**
     * Add column.
     *
     * @param  string  $name
     * @param  string  $columnClass
     * @param  array   $options
     *
     * @return $this
     */
    public function addColumn(string $name, string $columnClass, array $options = []): static
    {
        if (array_key_exists($name, $this->columns)) {
            throw new InvalidArgumentException("Table already contains a column with name '$name'");
        }

        $this->columns[$name] = $this->instanceStorage->getColumn($columnClass)->setOptions(
            array_merge(['field' => $name], $options)
        );

        return $this;
    }

    /**
     * Retrieves the adapter.
     *
     * @return AbstractAdapter|null
     */
    public function getAdapter(): ?AbstractAdapter
    {
        return $this->adapter;
    }

    /**
     * Set the adapter.
     *
     * @param  AbstractAdapter  $adapter
     * @param  array            $options
     *
     * @return $this
     */
    public function setAdapter(AbstractAdapter $adapter, array $options = []): static
    {
        $this->adapter = $adapter->setOptions($options);
        return $this;
    }

    /**
     * Create adapter.
     *
     * @param  string  $adapterClass
     * @param  array   $options
     *
     * @return $this
     */
    public function createAdapter(string $adapterClass, array $options = []): static
    {
        $this->setAdapter($this->instanceStorage->getAdapter($adapterClass), $options);
        return $this;
    }

    /**
     * Generate configuration which will be provided to the JS library.
     *
     * @return array
     */
    public function getConfig(): array
    {
        $tableOptions = $this->getOptions();

        if (!array_key_exists('ajaxURL', $tableOptions) || empty($tableOptions['ajaxURL'])) {
            throw new InvalidArgumentException('The ajaxURL option must be set');
        }

        return [
            'selector' => $this->getSelector(),
            'options'  => array_merge($tableOptions, [
                'columns' => array_map(static function (AbstractColumn $column) {
                    return $column->getConfig();
                }, array_values($this->getColumns())),
            ]),
        ];
    }

    /**
     * Handle Request.
     *
     * @param  Request  $request
     *
     * @return JsonResponse|null
     */
    public function handleRequest(Request $request): ?JsonResponse
    {
        if ('tabulator' === $request->query->get('generator')
            || 'tabulator' === $request->headers->get(
                'X-Request-Generator'
            )
        ) {
            if (null === $this->getAdapter()) {
                throw new InvalidArgumentException('Missing Adapter to handle request');
            }

            $adapterQuery = $this->getQueryAdapter($request);

            $this->processSorting($request, $adapterQuery);

            $this->processFiltering($request, $adapterQuery);

            $adapterResult = $this->getAdapter()->getData($adapterQuery);

            $adapterData = $this->parser->parse($adapterResult->getData(), $this->getColumns());

            return new JsonResponse(
                $this->getOption('pagination') ? [
                    'data'      => $adapterData,
                    'last_row'  => $adapterResult->getTotalRecords(),
                    'last_page' => $adapterResult->getTotalPages(),
                ] : $adapterData
            );
        }

        return null;
    }

    /**
     * Configure Options.
     *
     * @param  OptionsResolver  $resolver
     *
     * @return void
     */
    private function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setDefaults([
                'ajaxURL'                => $this->request?->getRequestUri(),
                'ajaxConfig'             => Request::METHOD_POST,
                'ajaxContentType'        => 'json',
                'ajaxParams'             => ['generator' => 'tabulator'],
                'layout'                 => 'fitColumns',
                'pagination'             => true,
                'paginationMode'         => 'remote',
                'paginationSize'         => 25,
                'paginationButtonCount'  => 3,
                'paginationSizeSelector' => [10, 25, 50],
                'paginationCounter'      => 'rows',
                'filterMode'             => 'remote',
                'initialFilter'          => [],
                'sortMode'               => 'remote',
                'initialSort'            => [],
                'placeholder'            => false,
            ])
            ->setAllowedTypes('ajaxURL', ['string', 'null'])
            ->setAllowedTypes('ajaxConfig', 'string')
            ->setAllowedTypes('ajaxContentType', 'string')
            ->setAllowedTypes('ajaxParams', 'array')
            ->setAllowedTypes('layout', 'string')
            ->setAllowedTypes('pagination', 'bool')
            ->setAllowedTypes('paginationMode', 'string')
            ->setAllowedTypes('paginationSize', 'int')
            ->setAllowedTypes('paginationButtonCount', 'int')
            ->setAllowedTypes('paginationSizeSelector', 'array')
            ->setAllowedTypes('paginationCounter', 'string')
            ->setAllowedTypes('filterMode', 'string')
            ->setAllowedTypes('initialFilter', 'array')
            ->setAllowedTypes('sortMode', 'string')
            ->setAllowedTypes('initialSort', 'array')
            ->setAllowedTypes('placeholder', ['string', 'bool']);
    }

    /**
     * Process filtering.
     *
     * @param  Request       $request
     * @param  QueryAdapter  $adapterQuery
     *
     * @return void
     */
    private function processFiltering(Request $request, QueryAdapter $adapterQuery): void
    {
        $requestFilter = !empty($request->query->all('filter')) ? $request->query->all('filter')
            : $request->getPayload()->all('filter');

        if (count($requestFilter) > 0) {
            foreach ($requestFilter as $value) {
                if (isset($value['value'])) {
                    $this->filterAnd($adapterQuery, $value);
                } else {
                    $this->filterOr($adapterQuery, $value);
                }
            }
        }
    }

    /**
     * Filter OR.
     *
     * @param  QueryAdapter  $adapterQuery
     * @param  mixed         $value
     *
     * @return void
     */
    private function filterOr(QueryAdapter $adapterQuery, mixed $value): void
    {
        foreach ($value as $item) {
            $filterColumn = $this->getColumn($item['field']);
            $filterColumnName = $filterColumn->getOption('field');

            if (false === $filterColumn->getOption('filterable')) {
                throw new InvalidArgumentException(
                    "Filtering by the '$filterColumnName' column is disabled"
                );
            }

            $adapterQuery->getFilteringBag()->addFilter(
                new FilteringItem()
                    ->setColumn($filterColumn)
                    ->setType(FilteringType::from($item['type']))
                    ->setValue($item['value']),
                FilteringComparison::OR
            );
        }
    }

    /**
     * Process filter AND.
     *
     * @param  QueryAdapter  $adapterQuery
     * @param  mixed         $value
     *
     * @return void
     */
    private function filterAnd(QueryAdapter $adapterQuery, mixed $value): void
    {
        $filterColumn = $this->getColumn($value['field']);
        $filterColumnName = $filterColumn->getOption('field');

        if (false === $filterColumn->getOption('filterable')) {
            throw new InvalidArgumentException(
                "Filtering by the '$filterColumnName' column is disabled"
            );
        }

        $adapterQuery->getFilteringBag()->addFilter(
            new FilteringItem()
                ->setColumn($filterColumn)
                ->setType(FilteringType::from($value['type']))
                ->setValue($value['value']),
            FilteringComparison::AND
        );
    }

    /**
     * Process sorting.
     *
     * @param  Request       $request
     * @param  QueryAdapter  $adapterQuery
     *
     * @return void
     */
    private function processSorting(Request $request, QueryAdapter $adapterQuery): void
    {
        $requestSort = !empty($request->query->all('sort'))
            ? $request->query->all('sort')
            : $request->getPayload()->all('sort');

        if (count($requestSort) > 0) {
            foreach ($requestSort as $value) {
                $adapterQuery->getSortingBag()->addSortingItem(
                    new SortingItem()
                        ->setColumn($this->getColumn($value['field']))
                        ->setDirection(SortingDirection::from($value['dir']))
                );
            }
        }
    }

    /**
     * Generate QueryAdapter.
     *
     * @param  Request  $request
     *
     * @return QueryAdapter
     */
    private function getQueryAdapter(Request $request): QueryAdapter
    {
        return new QueryAdapter()
            ->setPagination($this->getOption('pagination'))
            ->setPaginationSize($request->query->get('size') ?? $request->getPayload()->get('size'))
            ->setPaginationPage($request->query->get('page') ?? $request->getPayload()->get('page'))
            ->setPayload($request->getPayload());
    }

}
