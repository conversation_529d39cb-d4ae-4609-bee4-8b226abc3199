// bootstrap-rating - v1.3.2 - (c) 2016 dreyescat 
// https://github.com/dreyescat/bootstrap-rating MIT
!function(a,b){"use strict";function c(c,e){this.$input=a(c),this.$rating=a("<span></span>").css({cursor:"default"}).insertBefore(this.$input),this.options=function(c){return c.start=parseInt(c.start,10),c.start=isNaN(c.start)?b:c.start,c.stop=parseInt(c.stop,10),c.stop=isNaN(c.stop)?c.start+d||b:c.stop,c.step=parseInt(c.step,10)||b,c.fractions=Math.abs(parseInt(c.fractions,10))||b,c.scale=Math.abs(parseInt(c.scale,10))||b,c=a.extend({},a.fn.rating.defaults,c),c.filledSelected=c.filledSelected||c.filled,c}(a.extend({},this.$input.data(),e)),this._init()}var d=5;c.prototype={_init:function(){for(var c=this,d=this.$input,e=this.$rating,f=function(a){return function(c){d.prop("disabled")||d.prop("readonly")||d.data("readonly")!==b||a.call(this,c)}},g=1;g<=this._rateToIndex(this.options.stop);g++){var h=a('<div class="rating-symbol"></div>').css({display:"inline-block",position:"relative"});a('<div class="rating-symbol-background '+this.options.empty+'"></div>').appendTo(h),a('<div class="rating-symbol-foreground"></div>').append("<span></span>").css({display:"inline-block",position:"absolute",overflow:"hidden",left:0,right:0,width:0}).appendTo(h),e.append(h),this.options.extendSymbol.call(h,this._indexToRate(g))}this._updateRate(d.val()),d.on("change",function(){c._updateRate(a(this).val())});var i,j=function(b){var d=a(b.currentTarget),e=Math.abs((b.pageX||b.originalEvent.touches[0].pageX)-(("rtl"===d.css("direction")&&d.width())+d.offset().left));return e=e>0?e:.1*c.options.scale,d.index()+e/d.width()};e.on("mousedown touchstart",".rating-symbol",f(function(a){d.val(c._indexToRate(j(a))).change()})).on("mousemove touchmove",".rating-symbol",f(function(d){var e=c._roundToFraction(j(d));e!==i&&(i!==b&&a(this).trigger("rating.rateleave"),i=e,a(this).trigger("rating.rateenter",[c._indexToRate(i)])),c._fillUntil(e)})).on("mouseleave touchend",".rating-symbol",f(function(){i=b,a(this).trigger("rating.rateleave"),c._fillUntil(c._rateToIndex(parseFloat(d.val())))}))},_fillUntil:function(a){var b=this.$rating,c=Math.floor(a);b.find(".rating-symbol-background").css("visibility","visible").slice(0,c).css("visibility","hidden");var d=b.find(".rating-symbol-foreground");d.width(0),d.slice(0,c).width("auto").find("span").attr("class",this.options.filled),d.eq(a%1?c:c-1).find("span").attr("class",this.options.filledSelected),d.eq(c).width(a%1*100+"%")},_indexToRate:function(a){return this.options.start+Math.floor(a)*this.options.step+this.options.step*this._roundToFraction(a%1)},_rateToIndex:function(a){return(a-this.options.start)/this.options.step},_roundToFraction:function(a){var b=Math.ceil(a%1*this.options.fractions)/this.options.fractions,c=Math.pow(10,this.options.scale);return Math.floor(a)+Math.floor(b*c)/c},_contains:function(a){var b=this.options.step>0?this.options.start:this.options.stop,c=this.options.step>0?this.options.stop:this.options.start;return a>=b&&c>=a},_updateRate:function(a){var b=parseFloat(a);this._contains(b)&&(this._fillUntil(this._rateToIndex(b)),this.$input.val(b))},rate:function(a){return a===b?this.$input.val():void this._updateRate(a)}},a.fn.rating=function(b){var d,e=Array.prototype.slice.call(arguments,1);return this.each(function(){var f=a(this),g=f.data("rating");g||f.data("rating",g=new c(this,b)),"string"==typeof b&&"_"!==b[0]&&(d=g[b].apply(g,e))}),d||this},a.fn.rating.defaults={filled:"glyphicon glyphicon-star",filledSelected:b,empty:"glyphicon glyphicon-star-empty",start:0,stop:d,step:1,fractions:1,scale:3,extendSymbol:function(a){}},a(function(){a("input.rating").rating()})}(jQuery);