<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Sorter;

/**
 * Class SortingBag.
 *
 * This class is responsible for managing and storing sorting conditions.
 * It provides methods to add and retrieve sortable function, allowing for flexible filtering logic.
 */
class SortingBag
{

    private array $sortingItems = [];

    /**
     * Add sorting item.
     *
     * @param  SortingItem  $sortingItem
     *
     * @return $this
     */
    public function addSortingItem(SortingItem $sortingItem): SortingBag
    {
        $this->sortingItems[] = $sortingItem;
        return $this;
    }

    /**
     * Retrieves sorting items.
     *
     * @return array
     */
    public function getSortingItems(): array
    {
        return $this->sortingItems;
    }

    /**
     * Check if there are any active sorting conditions.
     *
     * @return bool
     */
    public function hasSorting(): bool
    {
        return count($this->sortingItems) > 0;
    }

}
