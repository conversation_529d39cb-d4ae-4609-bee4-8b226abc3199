{% extends 'base.html.twig' %}

{% block title %}Oops!{% endblock %}

{% block body %}
    <div class="account-pages my-5 pt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="text-center mb-5">
                        <h1 class="display-2 fw-medium">
                            {{ status_code }}
                        </h1>
                        <h2 class="text-uppercase">{{ status_text }}</h2>
                        <p class="font-size-18">
                            {% if status_code == '404' %}
                                <span>Sorry, the page you are looking for could not be found. Please try again later.</span>
                            {% elseif status_code == '403' %}
                                <span>You do not have permission to access this page. Please contact support if you believe this is an error.</span>
                            {% elseif status_code == '500' %}
                                <span>An internal server error occurred. Please try again later or contact support.</span>
                            {% elseif status_code == '503' %}
                                <span>The service is currently unavailable. Please try again later.</span>
                            {% else %}
                                <span>Something went wrong. Please try again later or contact support.</span>
                            {% endif %}
                        </p>
                        <div class="mt-5 text-center">
                            <button class="btn btn-primary waves-effect waves-light" onclick="goBack()">
                                Back to Home
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-8 col-xl-6">
                    <div>
                        <img src="{{ asset('resources/theme/images/error-img.png') }}" alt="" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack () {
            const currentUrl = window.location.pathname

            if (currentUrl.startsWith('/admin')) {
                window.location.href = '{{ app_admin_base_url }}'
            } else if (currentUrl.startsWith('/client')) {
                window.location.href = '{{ app_client_base_url }}'
            } else if (currentUrl.startsWith('/reseller')) {
                window.location.href = '{{ app_reseller_base_url }}'
            } else if (currentUrl.startsWith('/partner')) {
                window.location.href = '{{ app_partner_base_url }}'
            } else {
                window.location.href = '{{ app_portal_base_url }}'
            }
        }
    </script>
{% endblock %}
