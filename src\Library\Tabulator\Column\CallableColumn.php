<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Column;

use App\Library\Tabulator\Base\AbstractColumn;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class CallableColumn.
 *
 * This class extends the AbstractColumn and allows the column's content to be processed
 * using a user-defined callable function.
 */
class CallableColumn extends AbstractColumn
{

    protected function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver->setRequired('callable')->setAllowedTypes('callable', 'callable');
    }

    public function prepareContent($value): mixed
    {
        return call_user_func($this->getOption('callable'), $value);
    }

}
