{% extends 'domains/portal/shared/layouts/main.html.twig' %}

{% block title %}Welcome{% endblock %}

{% block stylesheets %}
    {{ parent() }}

    <style>
        body {
            scroll-behavior: smooth;
        }
        .hero {
            background: linear-gradient(135deg, #4f46e5, #3b82f6);
            color: white;
            padding: 160px 0 120px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .hero::after {
            content: '';
            position: absolute;
            bottom: -50px;
            left: 0;
            right: 0;
            height: 100px;
            background: url('https://www.svgrepo.com/show/448267/wave.svg') no-repeat center;
            background-size: cover;
        }
        .hero h1 {
            font-size: 4rem;
            font-weight: 700;
        }
        .hero p {
            font-size: 1.3rem;
        }
        .btn-primary {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }
        .feature-icon {
            font-size: 3rem;
            color: #4f46e5;
        }
        .feature-box {
            background: white;
            border-radius: 1rem;
            padding: 30px;
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-box:hover {
            transform: translateY(-8px);
            box-shadow: 0 1.25rem 2rem rgba(0, 0, 0, 0.08);
        }
        .card-pricing {
            border-radius: 1rem;
            border: none;
            transition: all 0.3s ease-in-out;
        }
        .card-pricing:hover {
            transform: translateY(-10px);
            box-shadow: 0 0.75rem 1.25rem rgba(0, 0, 0, 0.1);
        }
        .testimonials {
            background: #f1f5f9;
            padding: 80px 0;
        }
        .testimonial-card {
            background: white;
            border-radius: 1rem;
            padding: 30px;
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        .gradient-divider {
            height: 6px;
            background: linear-gradient(to right, #6366f1, #3b82f6);
            margin: 60px 0;
        }
        footer {
            background-color: #1f2937;
            color: white;
            padding: 40px 0;
        }
    </style>
{% endblock %}

{% block content %}
    <div id="x-content">
        <div class="bg-white">
            <section class="hero">
                <div class="container position-relative">
                    <h1>ZenShop</h1>
                    <p class="lead mt-3 mb-4">High-performance and fully flexible automated e-commerce platform built
                        for
                        growth</p>
                    <a href="#pricing" class="btn btn-light btn-lg text-primary fw-semibold">See Plans</a>
                </div>
            </section>

            <section id="features" class="py-5 bg-white">
                <div class="container">
                    <h2 class="text-center fw-bold mb-5">Why Choose ZenShop?</h2>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="feature-box text-center h-100">
                                <div class="feature-icon mb-3"><i class="fas fa-bolt"></i></div>
                                <h5>Lightning Speed</h5>
                                <p>Optimized for blazing fast performance with edge caching and SSR capabilities.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-box text-center h-100">
                                <div class="feature-icon mb-3"><i class="fas fa-layer-group"></i></div>
                                <h5>Modular Architecture</h5>
                                <p>Adapt to any business model with flexible modules, APIs, and integrations.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-box text-center h-100">
                                <div class="feature-icon mb-3"><i class="fas fa-robot"></i></div>
                                <h5>Automation First</h5>
                                <p>Streamline workflows with AI-driven automation for orders, inventory & marketing.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="testimonials" class="testimonials">
                <div class="container">
                    <h2 class="text-center fw-bold mb-5">What Our Clients Say</h2>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p>"ZenShop revolutionized our e-commerce strategy. It saved us time and multiplied
                                    revenue
                                    within weeks!"</p>
                                <small class="text-muted">— Anna, CEO of ModernMart</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p>"From integration to launch, everything was seamless. Performance is top-notch."</p>
                                <small class="text-muted">— John, Developer at RapidWear</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="testimonial-card">
                                <p>"ZenShop's automation gave us more time to focus on growth. Love the clean
                                    interface!"</p>
                                <small class="text-muted">— Lisa, Founder of Craftory</small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="pricing" class="py-5 bg-white">
                <div class="container text-center">
                    <h2 class="fw-bold mb-5">Pricing Plans</h2>
                    <div class="row g-4 justify-content-center">
                        <div class="col-md-4">
                            <div class="card card-pricing">
                                <div class="card-body">
                                    <h4 class="card-title">Starter</h4>
                                    <h3 class="my-3">$19/mo</h3>
                                    <ul class="list-unstyled">
                                        <li>✔ 100 Products</li>
                                        <li>✔ Basic Automation</li>
                                        <li>✔ Email Support</li>
                                    </ul>
                                    <a href="#" class="btn btn-outline-primary mt-3">Choose Plan</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-pricing border-primary shadow-lg">
                                <div class="card-body">
                                    <h4 class="card-title">Pro</h4>
                                    <h3 class="my-3">$49/mo</h3>
                                    <ul class="list-unstyled">
                                        <li>✔ Unlimited Products</li>
                                        <li>✔ Full Automation</li>
                                        <li>✔ Priority Support</li>
                                    </ul>
                                    <a href="#" class="btn btn-primary mt-3">Get Pro</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
    <div id="x-modal">
        <div class="modal fade" id="subscribeModal" tabindex="-1" aria-labelledby="subscribeModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-bottom-0">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <div class="avatar-md mx-auto mb-4">
                                <div class="avatar-title bg-light rounded-circle text-primary h1">
                                    <i class="mdi mdi-email-open"></i>
                                </div>
                            </div>

                            <div class="row justify-content-center">
                                <div class="col-xl-10">
                                    <h4 class="text-primary">Subscribe !</h4>
                                    <p class="text-muted font-size-14 mb-4">
                                        Subscribe our newletter and get notification to stay update.
                                    </p>

                                    <div class="input-group bg-light rounded">
                                        <input type="email" class="form-control bg-transparent border-0"
                                               placeholder="Enter Email address"
                                               aria-label="Recipient's username"
                                               aria-describedby="button-addon2">

                                        <button class="btn btn-primary" type="button" id="button-addon2">
                                            <i class="bx bxs-paper-plane"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        $(document).ready(function () {
            $('#subscribeModal').modal('show')
        })
    </script>
{% endblock %}
