<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Adapter;

use App\Library\Tabulator\Filter\FilteringBag;
use App\Library\Tabulator\Sorter\SortingBag;
use Symfony\Component\HttpFoundation\InputBag;

/**
 * Class QueryAdapter.
 *
 * Represents a query to be executed by an adapter. This class encapsulates various query parameters
 * such as pagination settings, sorting options, filtering criteria, and any additional payload
 * data that may be needed to perform a specific query against the data source.
 */
class QueryAdapter
{

    private bool $pagination = false;

    private ?int $paginationPage = null;

    private ?int $paginationSize = null;

    private SortingBag $sortingBag;

    private FilteringBag $filteringBag;

    private ?InputBag $payload = null;

    public function __construct()
    {
        $this->sortingBag = new SortingBag();
        $this->filteringBag = new FilteringBag();
    }

    /**
     * Check is pagination exist or not.
     *
     * @return bool
     */
    public function isPagination(): bool
    {
        return $this->pagination;
    }

    /**
     * Set the pagination.
     *
     * @param  bool  $pagination
     *
     * @return $this
     */
    public function setPagination(bool $pagination): static
    {
        $this->pagination = $pagination;

        return $this;
    }

    /**
     * Retrieves the pagination page.
     *
     * @return int|null
     */
    public function getPaginationPage(): ?int
    {
        return $this->paginationPage;
    }

    /**
     * Set the pagination page.
     *
     * @param  int|null  $paginationPage
     *
     * @return $this
     */
    public function setPaginationPage(?int $paginationPage): static
    {
        $this->paginationPage = $paginationPage;

        return $this;
    }

    /**
     * Retrieves the pagination size.
     *
     * @return int|null
     */
    public function getPaginationSize(): ?int
    {
        return $this->paginationSize;
    }

    /**
     * Set the pagination size.
     *
     * @param  int|null  $paginationSize
     *
     * @return $this
     */
    public function setPaginationSize(?int $paginationSize): static
    {
        $this->paginationSize = $paginationSize;

        return $this;
    }

    /**
     * Retrieves the sorting bag.
     *
     * @return SortingBag
     */
    public function getSortingBag(): SortingBag
    {
        return $this->sortingBag;
    }

    /**
     * Retrieves the filtering bag.
     *
     * @return FilteringBag
     */
    public function getFilteringBag(): FilteringBag
    {
        return $this->filteringBag;
    }

    /**
     * Retrieves the payload.
     *
     * @return InputBag|null
     */
    public function getPayload(): ?InputBag
    {
        return $this->payload;
    }

    /**
     * Set the payload.
     *
     * @param  InputBag|null  $payload
     *
     * @return $this
     */
    public function setPayload(?InputBag $payload): static
    {
        $this->payload = $payload;

        return $this;
    }

}
