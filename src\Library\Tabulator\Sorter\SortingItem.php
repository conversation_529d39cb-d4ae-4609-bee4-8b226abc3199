<?php

declare(strict_types=1);

namespace App\Library\Tabulator\Sorter;

use App\Library\Tabulator\Base\AbstractColumn;
use App\Library\Tabulator\Enum\SortingDirection;

/**
 * Class SortingItem.
 *
 * This class represents a sortable item, consisting of a column and a sorting direction.
 */
class SortingItem
{

    private AbstractColumn $column;

    private SortingDirection $direction;

    /**
     * Retrieves the sortable column.
     *
     * @return AbstractColumn
     */
    public function getColumn(): AbstractColumn
    {
        return $this->column;
    }

    /**
     * Set the sortable column.
     *
     * @param  AbstractColumn  $column
     *
     * @return static
     */
    public function setColumn(AbstractColumn $column): static
    {
        $this->column = $column;

        return $this;
    }

    /**
     * Retrieves the sorting direction.
     *
     * @return SortingDirection
     */
    public function getDirection(): SortingDirection
    {
        return $this->direction;
    }

    /**
     * Set the sorting direction.
     *
     * @param  SortingDirection  $direction
     *
     * @return static
     */
    public function setDirection(SortingDirection $direction): static
    {
        $this->direction = $direction;

        return $this;
    }

}
